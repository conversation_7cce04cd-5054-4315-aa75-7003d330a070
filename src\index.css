:root {
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;
  color-scheme: light dark;
  color: rgba(255, 255, 255, 0.87);
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-text-size-adjust: 100%;
  --spinner-animation-speed: 2s;
}

body {
  padding: 0;
  margin: 0;
}

#app {
  background-color: #212228;
  background-image: linear-gradient(#292a30 0.1em, transparent 0.1em),
    linear-gradient(90deg, #292a30 0.1em, transparent 0.1em);
  background-size: 4em 4em;
  height: 100vh;
  position: relative;
  overflow: auto;
}

.card {
  width: 400px;
  border-radius: 5px;
  cursor: pointer;
  box-shadow: 0 1px 1px hsl(0deg 0% 0% / 0.075),
    0 2px 2px hsl(0deg 0% 0% / 0.075), 0 4px 4px hsl(0deg 0% 0% / 0.075),
    0 8px 8px hsl(0deg 0% 0% / 0.075), 0 16px 16px hsl(0deg 0% 0% / 0.075);
  position: absolute;
}

.card-body {
  padding: 1em;
  border-radius: 0 0 5px 5px;
}

.card-body textarea {
  background-color: inherit;
  border: none;
  width: 100%;
  height: 100%;
  resize: none;
  font-size: 16px;
}

textarea:focus {
  background-color: inherit;
  outline: none;
  width: 100%;
  height: 100%;
}

.card-header {
  background-color: #9bd1de;
  border-radius: 5px 5px 0 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 5px;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.spinner {
  animation: spin var(--spinner-animation-speed) linear infinite;
}

.card-saving {
  display: flex;
  align-items: center;
  gap: 5px;
}

#controls {
  display: flex;
  flex-direction: column;
  gap: 1em;
  align-items: center;
  position: fixed;
  left: 1em;
  top: 50%;
  transform: translateY(-50%);
  background-color: #35363e;
  padding: 1em 1em;
  border-radius: 40px;

  box-shadow: 0 1px 1px hsl(0deg 0% 0% / 0.075),
    0 2px 2px hsl(0deg 0% 0% / 0.075), 0 4px 4px hsl(0deg 0% 0% / 0.075),
    0 8px 8px hsl(0deg 0% 0% / 0.075), 0 16px 16px hsl(0deg 0% 0% / 0.075);
  z-index: 10000;
}

#add-btn {
  background-color: rgba(107, 107, 107, 1);
  display: flex;
  justify-content: center;
  align-items: center;
  height: 40px;
  width: 40px;
  border-radius: 50%;
  cursor: pointer;
  transition: 0.3s;
}

#add-btn:hover {
  height: 45px;
  width: 45px;
}

.color {
  background-color: grey;
  border: 2px solid;
  height: 40px;
  width: 40px;
  border-radius: 50%;
  justify-content: center;
  align-items: center;

  cursor: pointer;
  transition: 0.3s;
}

.color:hover {
  height: 45px;
  width: 45px;
}

/* Login Page Styles */
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background-color: #212228;
  background-image: linear-gradient(#292a30 0.1em, transparent 0.1em),
    linear-gradient(90deg, #292a30 0.1em, transparent 0.1em);
  background-size: 4em 4em;
}

.login-form {
  background-color: #35363e;
  padding: 2.5rem;
  border-radius: 12px;
  width: 400px;
  max-width: 90vw;
  box-shadow: 0 1px 1px hsl(0deg 0% 0% / 0.075),
    0 2px 2px hsl(0deg 0% 0% / 0.075), 0 4px 4px hsl(0deg 0% 0% / 0.075),
    0 8px 8px hsl(0deg 0% 0% / 0.075), 0 16px 16px hsl(0deg 0% 0% / 0.075);
}

.login-title {
  color: rgba(255, 255, 255, 0.87);
  text-align: center;
  margin-bottom: 2rem;
  font-size: 1.5rem;
  font-weight: 500;
}

.login-input {
  width: 100%;
  padding: 0.75rem 1rem;
  margin: 0.75rem 0;
  background-color: #212228;
  border: 1px solid #404040;
  border-radius: 8px;
  color: rgba(255, 255, 255, 0.87);
  font-size: 16px;
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
  box-sizing: border-box;
}

.login-input:focus {
  outline: none;
  border-color: #6b6b6b;
  box-shadow: 0 0 0 2px rgba(107, 107, 107, 0.2);
}

.login-input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.login-button {
  width: 100%;
  padding: 0.75rem 1rem;
  margin: 1rem 0;
  background-color: #6b6b6b;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s ease, transform 0.2s ease;
}

.login-button:hover:not(:disabled) {
  background-color: #7a7a7a;
  transform: translateY(-1px);
}

.login-button:active:not(:disabled) {
  transform: translateY(0);
}

.login-button:disabled {
  background-color: #4a4a4a;
  cursor: not-allowed;
  opacity: 0.7;
}

.login-error {
  color: #ff6b6b;
  text-align: center;
  margin: 0.5rem 0;
  font-size: 14px;
}

.login-switch {
  color: rgba(255, 255, 255, 0.87);
  text-align: center;
  margin-top: 1.5rem;
}

.login-switch-button {
  background: none;
  border: none;
  color: #6b6b6b;
  cursor: pointer;
  text-decoration: underline;
  font-size: inherit;
  transition: color 0.3s ease;
}

.login-switch-button:hover {
  color: #7a7a7a;
}

/* Header/Logout Styles */
.app-header {
  position: fixed;
  top: 1rem;
  right: 1rem;
  z-index: 10000;
  display: flex;
  align-items: center;
  gap: 1rem;
  background-color: #35363e;
  padding: 0.75rem 1.25rem;
  border-radius: 12px;
  box-shadow: 0 1px 1px hsl(0deg 0% 0% / 0.075),
    0 2px 2px hsl(0deg 0% 0% / 0.075), 0 4px 4px hsl(0deg 0% 0% / 0.075),
    0 8px 8px hsl(0deg 0% 0% / 0.075), 0 16px 16px hsl(0deg 0% 0% / 0.075);
}

.welcome-text {
  color: rgba(255, 255, 255, 0.87);
  font-weight: 500;
}

.logout-button {
  padding: 0.5rem 1rem;
  background-color: #6b6b6b;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.3s ease, transform 0.2s ease;
}

.logout-button:hover {
  background-color: #7a7a7a;
  transform: translateY(-1px);
}

.logout-button:active {
  transform: translateY(0);
}
