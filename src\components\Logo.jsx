const Logo = ({ 
  size = 32, 
  variant = "blue", 
  includeText = false, 
  className = "",
  style = {} 
}) => {
  // Color variants matching the app's sticky note colors
  const colorVariants = {
    blue: {
      headerColor: "#9BD1DE",
      bodyColor: "#A6DCE9",
      foldColor: "#7BC4D4"
    },
    yellow: {
      headerColor: "#FFEFBE",
      bodyColor: "#FFF5DF",
      foldColor: "#F5E09F"
    },
    green: {
      headerColor: "#AFDA9F",
      bodyColor: "#BCDEAF",
      foldColor: "#9BC98A"
    },
    purple: {
      headerColor: "#FED0FD",
      bodyColor: "#FEE5FD",
      foldColor: "#F8B8F7"
    },
    monochrome: {
      headerColor: "#FFFFFF",
      bodyColor: "#F5F5F5",
      foldColor: "#E0E0E0"
    }
  };

  const colors = colorVariants[variant] || colorVariants.blue;
  const textColor = variant === "monochrome" ? "#333333" : "#18181A";
  
  // Calculate dimensions based on size
  const logoSize = parseInt(size);
  const viewBoxSize = includeText ? `0 0 ${logoSize * 3} ${logoSize}` : `0 0 ${logoSize} ${logoSize}`;
  const noteSize = logoSize * 0.625; // 40/64 ratio from original
  const noteX = logoSize * 0.125; // 8/64 ratio
  const noteY = logoSize * 0.1875; // 12/64 ratio
  const cornerSize = logoSize * 0.125; // 8/64 ratio
  const strokeWidth = Math.max(1, logoSize * 0.023); // 1.5/64 ratio, minimum 1px
  
  // Text positioning for includeText variant
  const textX = logoSize + logoSize * 0.15;
  const titleY = logoSize * 0.4;
  const subtitleY = logoSize * 0.65;
  const titleSize = logoSize * 0.28;
  const subtitleSize = logoSize * 0.19;

  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox={viewBoxSize}
      width={includeText ? logoSize * 3 : logoSize}
      height={logoSize}
      className={className}
      style={style}
    >
      <defs>
        {/* Shadow filter for depth */}
        <filter id={`shadow-${variant}-${size}`} x="-50%" y="-50%" width="200%" height="200%">
          <feDropShadow 
            dx={logoSize * 0.015} 
            dy={logoSize * 0.03} 
            stdDeviation={logoSize * 0.023} 
            floodColor="#000000" 
            floodOpacity={variant === "monochrome" ? "0.3" : "0.15"}
          />
        </filter>
        
        {/* Gradient for note body */}
        <linearGradient id={`noteGradient-${variant}-${size}`} x1="0%" y1="0%" x2="0%" y2="100%">
          <stop offset="0%" stopColor={colors.headerColor}/>
          <stop offset="100%" stopColor={colors.bodyColor}/>
        </linearGradient>
        
        {/* Gradient for folded corner */}
        <linearGradient id={`foldGradient-${variant}-${size}`} x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" stopColor={colors.foldColor}/>
          <stop offset="100%" stopColor={colors.headerColor}/>
        </linearGradient>
      </defs>
      
      {/* Main sticky note body */}
      <rect 
        x={noteX} 
        y={noteY} 
        width={noteSize} 
        height={noteSize} 
        rx={logoSize * 0.0625} 
        ry={logoSize * 0.0625}
        fill={`url(#noteGradient-${variant}-${size})`}
        filter={`url(#shadow-${variant}-${size})`}
        transform={`rotate(-8 ${logoSize * 0.4375} ${logoSize * 0.5})`}
      />
      
      {/* Folded corner */}
      <path 
        d={`M ${noteX + noteSize * 0.8} ${noteY} L ${noteX + noteSize + cornerSize} ${noteY} L ${noteX + noteSize * 0.8} ${noteY + cornerSize} Z`}
        fill={`url(#foldGradient-${variant}-${size})`}
        transform={`rotate(-8 ${logoSize * 0.4375} ${logoSize * 0.5})`}
      />
      
      {/* Text lines inside the note */}
      <g 
        transform={`rotate(-8 ${logoSize * 0.4375} ${logoSize * 0.5})`} 
        stroke={textColor} 
        strokeWidth={strokeWidth} 
        strokeLinecap="round"
      >
        <line 
          x1={noteX + noteSize * 0.15} 
          y1={noteY + noteSize * 0.25} 
          x2={noteX + noteSize * 0.75} 
          y2={noteY + noteSize * 0.25}
        />
        <line 
          x1={noteX + noteSize * 0.15} 
          y1={noteY + noteSize * 0.4} 
          x2={noteX + noteSize * 0.6} 
          y2={noteY + noteSize * 0.4}
        />
        <line 
          x1={noteX + noteSize * 0.15} 
          y1={noteY + noteSize * 0.55} 
          x2={noteX + noteSize * 0.7} 
          y2={noteY + noteSize * 0.55}
        />
      </g>
      
      {/* App name text (if includeText is true) */}
      {includeText && (
        <g>
          <text 
            x={textX} 
            y={titleY} 
            fontFamily="Inter, system-ui, sans-serif" 
            fontSize={titleSize} 
            fontWeight="500" 
            fill="rgba(255, 255, 255, 0.87)"
          >
            Sticky Notes
          </text>
          <text 
            x={textX} 
            y={subtitleY} 
            fontFamily="Inter, system-ui, sans-serif" 
            fontSize={subtitleSize} 
            fontWeight="400" 
            fill="rgba(255, 255, 255, 0.6)"
          >
            Digital Note Taking
          </text>
        </g>
      )}
    </svg>
  );
};

export default Logo;
